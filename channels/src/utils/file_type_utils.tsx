// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import type {FileInfo} from '@mattermost/types/files';

import Constants, {FileTypes} from 'utils/constants';

export function isGIFImage(extin: string): boolean {
    return extin.toLowerCase() === Constants.IMAGE_TYPE_GIF;
}

const removeQuerystringOrHash = (extin: string): string => {
    return extin.split(/[?#]/)[0];
};

export const getFileType = (extin: string): typeof FileTypes[keyof typeof FileTypes] => {
    const ext = removeQuerystringOrHash(extin.toLowerCase());

    if (Constants.TEXT_TYPES.indexOf(ext) > -1) {
        return FileTypes.TEXT;
    }

    if (Constants.IMAGE_TYPES.indexOf(ext) > -1) {
        return FileTypes.IMAGE;
    }

    if (Constants.AUDIO_TYPES.indexOf(ext) > -1) {
        return FileTypes.AUDIO;
    }

    if (Constants.VIDEO_TYPES.indexOf(ext) > -1) {
        return FileTypes.VIDEO;
    }

    if (Constants.SPREADSHEET_TYPES.indexOf(ext) > -1) {
        return FileTypes.SPREADSHEET;
    }

    if (Constants.CODE_TYPES.indexOf(ext) > -1) {
        return FileTypes.CODE;
    }

    if (Constants.WORD_TYPES.indexOf(ext) > -1) {
        return FileTypes.WORD;
    }

    if (Constants.PRESENTATION_TYPES.indexOf(ext) > -1) {
        return FileTypes.PRESENTATION;
    }

    if (Constants.PDF_TYPES.indexOf(ext) > -1) {
        return FileTypes.PDF;
    }

    if (Constants.PATCH_TYPES.indexOf(ext) > -1) {
        return FileTypes.PATCH;
    }

    if (Constants.SVG_TYPES.indexOf(ext) > -1) {
        return FileTypes.SVG;
    }

    return FileTypes.OTHER;
};

export function getFileIconPath(fileInfo: FileInfo) {
    const fileType = getFileType(fileInfo.extension) as keyof typeof Constants.ICON_FROM_TYPE;

    let icon;
    if (fileType in Constants.ICON_FROM_TYPE) {
        icon = Constants.ICON_FROM_TYPE[fileType];
    } else {
        icon = Constants.ICON_FROM_TYPE.other;
    }

    return icon;
}

export function getCompassIconClassName(fileTypeIn: string, outline = true, large = false) {
    const fileType = fileTypeIn.toLowerCase() as keyof typeof Constants.ICON_FROM_TYPE;
    let icon = 'generic';

    if (fileType in Constants.ICON_NAME_FROM_TYPE) {
        icon = Constants.ICON_NAME_FROM_TYPE[fileType];
    }

    icon = icon === 'ppt' ? 'powerpoint' : icon;
    icon = icon === 'spreadsheet' ? 'excel' : icon;
    icon = icon === 'other' ? 'generic' : icon;

    return `icon-file-${icon}${outline ? '-outline' : ''}${large ? '-large' : ''}`;
}

export function getIconClassName(fileTypeIn: string) {
    const fileType = fileTypeIn.toLowerCase()as keyof typeof Constants.ICON_FROM_TYPE;

    if (fileType in Constants.ICON_NAME_FROM_TYPE) {
        return Constants.ICON_NAME_FROM_TYPE[fileType];
    }

    return 'generic';
}

export function loadImage(
    url: string,
    onLoad: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null,
    onProgress?: (completedPercentage: number) => any | null,
) {
    const request = new XMLHttpRequest();

    request.open('GET', url, true);
    request.responseType = 'arraybuffer';
    request.onload = onLoad;
    request.onprogress = (e) => {
        if (onProgress) {
            let total = 0;
            if (e.lengthComputable) {
                total = e.total;
            } else {
                total = parseInt((e.target as any).getResponseHeader('X-Uncompressed-Content-Length'), 10);
            }

            const completedPercentage = Math.round((e.loaded / total) * 100);

            (onProgress as any)(completedPercentage);
        }
    };

    request.send();
}
