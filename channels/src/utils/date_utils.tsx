// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import type {Locale} from 'date-fns';
import {ar} from 'date-fns/locale';
import moment from 'moment';

const MS_PER_SECOND = 1000;
const MS_PER_MINUTE = 60 * MS_PER_SECOND;
const MS_PER_HOUR = 60 * MS_PER_MINUTE;
const MS_PER_DAY = 24 * MS_PER_HOUR;

export enum TimeInformation {
    MILLISECONDS = 'm',
    SECONDS = 's',
    MINUTES = 'x',
    HOURS = 'h',
    DAYS = 'd',
    FUTURE = 'f',
    PAST = 'p'
}

export type TimeUnit = Exclude<TimeInformation, TimeInformation.FUTURE | TimeInformation.PAST>;
export type TimeDirection = TimeInformation.FUTURE | TimeInformation.PAST;

export function getDateForUnixTicks(ticks: number): Date {
    return new Date(ticks);
}

// returns Unix timestamp in milliseconds
export function getTimestamp(): number {
    return Date.now();
}

export function getRemainingDaysFromFutureTimestamp(timestamp?: number): number {
    const futureDate = new Date(timestamp as number);
    const utcFuture = Date.UTC(futureDate.getFullYear(), futureDate.getMonth(), futureDate.getDate());
    const today = new Date();
    const utcToday = Date.UTC(today.getFullYear(), today.getMonth(), today.getDate());

    return Math.floor((utcFuture - utcToday) / MS_PER_DAY);
}

export function addTimeToTimestamp(timestamp: number, type: TimeUnit, diff: number, timeline: TimeDirection) {
    let modifier = 1;
    switch (type) {
    case TimeInformation.SECONDS:
        modifier = MS_PER_SECOND;
        break;
    case TimeInformation.MINUTES:
        modifier = MS_PER_MINUTE;
        break;
    case TimeInformation.HOURS:
        modifier = MS_PER_HOUR;
        break;
    case TimeInformation.DAYS:
        modifier = MS_PER_DAY;
        break;
    }

    return timeline === TimeInformation.FUTURE ? timestamp + (diff * modifier) : timestamp - (diff * modifier);
}

/**
 * Verifies if a date is in a particular given range of days from today
 * @param timestamp date you want to check is in the range of the provided number of days from today
 * @param days number of days you want to check your date against to
 * @param timeline 'f' represents future, 'p' represents past
 * @returns boolean, true if your date is in the range of the provided number of days
 */
export function isDateWithinDaysRange(timestamp: number, days: number, timeline: TimeDirection): boolean {
    const today = new Date().getTime();
    const daysSince = Math.round((today - timestamp) / MS_PER_DAY);
    return timeline === TimeInformation.PAST ? daysSince <= days : daysSince >= days;
}

export function getLocaleDateFromUTC(timestamp: number, format = 'YYYY/MM/DD HH:mm:ss', userTimezone = '') {
    if (!timestamp) {
        return moment.now();
    }
    const timezone = userTimezone ? ' ' + moment().tz(userTimezone).format('z') : '';
    return moment.unix(timestamp).format(format) + timezone;
}

export function getDatePickerLocalesForDateFns(locale: string, loadedLocales: Record<string, Locale>) {
    if (locale === 'ar' && !loadedLocales.ar) {
        loadedLocales.ar = ar; // ✅ إضافة دعم العربية
    } else if (locale && !loadedLocales[locale]) {
        try {
            // Dynamic import for date-fns locales
            // eslint-disable-next-line @typescript-eslint/no-var-requires, import/no-dynamic-require
            loadedLocales[locale] = require(`date-fns/locale/${locale}/index.js`);
        } catch (e) {
            console.log(e); // eslint-disable-line no-console
        }
    }

    return loadedLocales;
}
