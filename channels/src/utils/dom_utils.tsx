// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';

import type {TextboxElement} from 'components/textbox';
import Constants from 'utils/constants';
import * as Keyboard from 'utils/keyboard';
import * as UserAgent from 'utils/user_agent';

const CLICKABLE_ELEMENTS = [
    'a',
    'button',
    'img',
    'svg',
    'audio',
    'video',
];

/**
 * check keydown event for line break combo. Should catch alt/option + enter not all browsers except Safari
 */
export function isUnhandledLineBreakKeyCombo(e: React.KeyboardEvent | KeyboardEvent): boolean {
    return Boolean(
        Keyboard.isKeyPressed(e, Constants.KeyCodes.ENTER) &&
        !e.shiftKey && // shift + enter is already handled everywhere, so don't handle again
        (e.altKey && !UserAgent.isSafari() && !Keyboard.cmdOrCtrlPressed(e)), // alt/option + enter is already handled in Safari, so don't handle again
    );
}

/**
 * insert a new line character at keyboard cursor (or overwrites selection)
 * WARNING: HAS DOM SIDE EFFECTS
 */
export function insertLineBreakFromKeyEvent(e: KeyboardEvent): string {
    const el = e.target as TextboxElement;
    const {selectionEnd, selectionStart, value} = el;

    // replace text selection (or insert if no selection) with new line character
    const newValue = `${value.slice(0, selectionStart!)}\n${value.slice(selectionEnd!)}`;

    // update value on DOM element immediately and restore key cursor to correct position
    el.value = newValue;
    setSelectionRange(el, selectionStart! + 1, selectionStart! + 1);

    // return the updated string so that component state can be updated
    return newValue;
}

export function placeCaretAtEnd(el: HTMLInputElement | HTMLTextAreaElement) {
    el.focus();
    el.selectionStart = el.value.length;
    el.selectionEnd = el.value.length;
}

export function scrollToCaret(el: HTMLInputElement | HTMLTextAreaElement) {
    el.scrollTop = el.scrollHeight;
}

function createHtmlElement(el: keyof HTMLElementTagNameMap) {
    return document.createElement(el);
}

function getElementComputedStyle(el: Element) {
    return getComputedStyle(el);
}

function addElementToDocument(el: Node) {
    document.body.appendChild(el);
}

export function copyTextAreaToDiv(textArea: HTMLTextAreaElement) {
    if (!textArea) {
        return null;
    }
    const copy = createHtmlElement('div');
    copy.textContent = textArea.value;
    const style = getElementComputedStyle(textArea);
    [
        'fontFamily',
        'fontSize',
        'fontWeight',
        'wordWrap',
        'whiteSpace',
        'borderLeftWidth',
        'borderTopWidth',
        'borderRightWidth',
        'borderBottomWidth',
        'paddingRight',
        'paddingLeft',
        'paddingTop',
    ].forEach((key) => {
        copy.style[key as keyof CSSStyleDeclaration] = style[key as keyof CSSStyleDeclaration];
    });
    copy.style.overflow = 'auto';
    copy.style.width = textArea.offsetWidth + 'px';
    copy.style.height = textArea.offsetHeight + 'px';
    copy.style.position = 'absolute';
    copy.style.left = textArea.offsetLeft + 'px';
    copy.style.top = textArea.offsetTop + 'px';
    addElementToDocument(copy);
    return copy;
}

function convertEmToPixels(el: Element, remNum: number): number {
    if (isNaN(remNum)) {
        return 0;
    }
    const styles = getElementComputedStyle(el);
    return remNum * parseFloat(styles.fontSize);
}

export function getCaretXYCoordinate(textArea: HTMLTextAreaElement) {
    if (!textArea) {
        return {x: 0, y: 0};
    }
    const start = textArea.selectionStart;
    const end = textArea.selectionEnd;
    const copy = copyTextAreaToDiv(textArea);
    const range = document.createRange();
    range.setStart(copy!.firstChild!, start);
    range.setEnd(copy!.firstChild!, end);
    const selection = document.getSelection();
    selection!.removeAllRanges();
    selection!.addRange(range);
    const rect = range.getClientRects();
    document.body.removeChild(copy!);
    textArea.selectionStart = start;
    textArea.selectionEnd = end;
    textArea.focus();
    return {
        x: Math.floor(rect[0].left - textArea.scrollLeft),
        y: Math.floor(rect[0].top - textArea.scrollTop),
    };
}

export function getViewportSize(win?: Window) {
    const w = win || window;
    if (w.innerWidth != null) {
        return {w: w.innerWidth, h: w.innerHeight};
    }
    const {clientWidth, clientHeight} = w.document.body;
    return {w: clientWidth, h: clientHeight};
}

export function offsetTopLeft(el: HTMLElement) {
    if (!(el instanceof HTMLElement)) {
        return {top: 0, left: 0};
    }
    const rect = el.getBoundingClientRect();
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    return {top: rect.top + scrollTop, left: rect.left + scrollLeft};
}

export function getSuggestionBoxAlgn(textArea: HTMLTextAreaElement, pxToSubstract = 0, alignWithTextBox = false) {
    if (!textArea || !(textArea instanceof HTMLElement)) {
        return {
            pixelsToMoveX: 0,
            pixelsToMoveY: 0,
        };
    }

    const {x: caretXCoordinateInTxtArea, y: caretYCoordinateInTxtArea} = getCaretXYCoordinate(textArea);
    const {w: viewportWidth, h: viewportHeight} = getViewportSize();
    const {offsetWidth: textAreaWidth} = textArea;

    const suggestionBoxWidth = Math.min(textAreaWidth, Constants.SUGGESTION_LIST_MAXWIDTH);

    // value in pixels for the offsetLeft for the textArea
    const {top: txtAreaOffsetTop, left: txtAreaOffsetLft} = offsetTopLeft(textArea);

    // how many pixels to the right should be moved the suggestion box
    let pxToTheRight = (caretXCoordinateInTxtArea) - (pxToSubstract);

    // the x coordinate in the viewport of the suggestion box border-right
    const xBoxRightCoordinate = caretXCoordinateInTxtArea + txtAreaOffsetLft + suggestionBoxWidth;

    if (alignWithTextBox) {
        // when the list should be aligned with the textbox just set this value to 0
        pxToTheRight = 0;
    } else if (xBoxRightCoordinate > viewportWidth) {
        // if the right-border edge of the suggestion box will overflow the x-axis viewport
        // stick the suggestion list to the very right of the TextArea
        pxToTheRight = textAreaWidth - suggestionBoxWidth;
    }

    return {

        // The rough location of the caret in the textbox
        pixelsToMoveX: Math.max(0, Math.round(pxToTheRight)),
        pixelsToMoveY: Math.round(caretYCoordinateInTxtArea),

        // The line height of the textbox is needed so that the SuggestionList can adjust its position to be below the current line in the textbox
        lineHeight: Number(getComputedStyle(textArea)?.lineHeight.replace('px', '')),

        placementShift: txtAreaOffsetTop + caretYCoordinateInTxtArea + Constants.SUGGESTION_LIST_MAXHEIGHT > viewportHeight - Constants.POST_AREA_HEIGHT,
    };
}

export function getPxToSubstract(char = '@') {
    // depending on the triggering character different values must be substracted
    if (char === '@') {
    // mention name padding-left 2.4rem as stated in suggestion-list__content .suggestion-list__item
        const mentionNamePaddingLft = convertEmToPixels(document.documentElement, Constants.MENTION_NAME_PADDING_LEFT);

        // half of width of avatar stated in .Avatar.Avatar-sm (24px)
        const avatarWidth = Constants.AVATAR_WIDTH * 0.5;
        return 5 + avatarWidth + mentionNamePaddingLft;
    } else if (char === '~') {
        return 39;
    } else if (char === ':') {
        return 32;
    }
    return 0;
}

export function setSelectionRange(input: HTMLInputElement | HTMLTextAreaElement, selectionStart: number, selectionEnd: number) {
    input.focus();
    input.setSelectionRange(selectionStart, selectionEnd);
}

export function setCaretPosition(input: HTMLInputElement | HTMLTextAreaElement, pos: number) {
    if (!input) {
        return;
    }

    setSelectionRange(input, pos, pos);
}

export async function copyToClipboard(data: string): Promise<void> {
    if (!data) {
        return;
    }

    // Attempt to use the newer clipboard API when possible
    const clipboard = navigator.clipboard;
    if (clipboard) {
        clipboard.writeText(data);
        return;
    }

    // creates a tiny temporary text area to copy text out of
    // see https://stackoverflow.com/a/30810322/591374 for details
    const textArea = document.createElement('textarea');
    textArea.style.position = 'fixed';
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.width = '2em';
    textArea.style.height = '2em';
    textArea.style.padding = '0';
    textArea.style.border = 'none';
    textArea.style.outline = 'none';
    textArea.style.boxShadow = 'none';
    textArea.style.background = 'transparent';
    textArea.value = data;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
    } catch (err) {
        console.warn('Failed to copy text to clipboard:', err); // eslint-disable-line no-console
    } finally {
        document.body.removeChild(textArea);
    }
}

export function moveCursorToEnd(e: React.MouseEvent | React.FocusEvent) {
    const target = e.target as HTMLInputElement | HTMLTextAreaElement;
    const val = target.value;
    if (val.length) {
        target.value = '';
        target.value = val;
    }
}

function isSelection() {
    const selection = window.getSelection();
    return selection!.type === 'Range';
}

export function isTextSelectedInPostOrReply(e: React.KeyboardEvent | KeyboardEvent) {
    const {id} = e.target as HTMLElement;

    if (id && (id.includes('post_textbox') || id.includes('reply_textbox'))) {
        return isSelection();
    }

    return false;
}

export function isClickableElement(element: Element): boolean {
    return CLICKABLE_ELEMENTS.includes(element.tagName.toLowerCase());
}

export function isElementClickable(element: Element): boolean {
    if (isClickableElement(element)) {
        return true;
    }

    if (element.parentElement) {
        return isElementClickable(element.parentElement);
    }

    return false;
}
