// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {getName} from 'country-list';
import crypto from 'crypto';
import type {Locale} from 'date-fns';
import {ar} from 'date-fns/locale';
import isNil from 'lodash/isNil';
import moment from 'moment';
import type React from 'react';

import type {Channel} from '@mattermost/types/channels';
import type {Address} from '@mattermost/types/cloud';
import type {Group} from '@mattermost/types/groups';
import type {GlobalState} from '@mattermost/types/store';
import type {Team} from '@mattermost/types/teams';
import type {UserProfile} from '@mattermost/types/users';

import {
    getChannel as getChannelAction,
    getChannelByNameAndTeamName,
    getChannelMember,
    joinChannel,
} from 'mattermost-redux/actions/channels';
import {getPost as getPostAction} from 'mattermost-redux/actions/posts';
import {getTeamByName as getTeamByNameAction} from 'mattermost-redux/actions/teams';
import {Client4} from 'mattermost-redux/client';
import {General} from 'mattermost-redux/constants';
import {createSelector} from 'mattermost-redux/selectors/create_selector';
import {
    getChannel,
    getChannelsNameMapInTeam,
    getMyChannelMemberships,
} from 'mattermost-redux/selectors/entities/channels';
import {getPost} from 'mattermost-redux/selectors/entities/posts';
import {isCollapsedThreadsEnabled} from 'mattermost-redux/selectors/entities/preferences';
import {
    getTeamByName,
    getTeamMemberships,
    isTeamSameWithCurrentTeam,
} from 'mattermost-redux/selectors/entities/teams';
import {getCurrentUser, getCurrentUserId, isFirstAdmin} from 'mattermost-redux/selectors/entities/users';
import {isSystemAdmin} from 'mattermost-redux/utils/user_utils';

import {searchForTerm} from 'actions/post_actions';
import {addUserToTeam} from 'actions/team_actions';
import {getCurrentLocale, getTranslations} from 'selectors/i18n';
import store from 'stores/redux_store';

import {focusPost} from 'components/permalink_view/actions';
import type {TextboxElement} from 'components/textbox';

import {getHistory} from 'utils/browser_history';
import Constants, {A11yCustomEventTypes} from 'utils/constants';
import type {A11yFocusEventDetail} from 'utils/constants';
import * as UserAgent from 'utils/user_agent';

import {joinPrivateChannelPrompt} from './channel_utils';

// Re-export utilities from split modules
export * from './theme_utils';
export * from './date_utils';
export * from './file_type_utils';
export * from './user_display_utils';
export * from './dom_utils';
export * from './text_utils';
export * from './validation_utils';

const CLICKABLE_ELEMENTS = [
    'a',
    'button',
    'img',
    'svg',
    'audio',
    'video',
];

// Functions moved to separate utility modules - see imports above

// Date/time and text utility functions moved to date_utils.tsx and text_utils.tsx

// File type utility functions moved to file_type_utils.tsx

// File icon and theme utility functions moved to file_type_utils.tsx and theme_utils.tsx

// Theme functions moved to theme_utils.tsx

// CSS and theme helper functions moved to theme_utils.tsx

// DOM utility functions moved to dom_utils.tsx

// DOM and validation utility functions moved to dom_utils.tsx and validation_utils.tsx

// Image loading and color utility functions moved to file_type_utils.tsx and theme_utils.tsx

// User display utility functions moved to user_display_utils.tsx

// Additional user display functions moved to user_display_utils.tsx

// Image URL functions moved to user_display_utils.tsx

export function imageURLForUser(userId: UserProfile['id'], lastPictureUpdate = 0) {
    return Client4.getProfilePictureUrl(userId, lastPictureUpdate);
}

export function defaultImageURLForUser(userId: UserProfile['id']) {
    return Client4.getDefaultProfilePictureUrl(userId);
}

// in contrast to Client4.getTeamIconUrl, for ui logic this function returns null if last_team_icon_update is unset
export function imageURLForTeam(team: Team) {
    return team.last_team_icon_update ? Client4.getTeamIconUrl(team.id, team.last_team_icon_update) : null;
}

// Converts a file size in bytes into a human-readable string of the form '123MB'.
export function fileSizeToString(bytes: number) {
    // it's unlikely that we'll have files bigger than this
    if (bytes > 1024 ** 4) {
        // check if file is smaller than 10 to display fractions
        if (bytes < (1024 ** 4) * 10) {
            return (Math.round((bytes / (1024 ** 4)) * 10) / 10) + 'TB';
        }
        return Math.round(bytes / (1024 ** 4)) + 'TB';
    } else if (bytes > 1024 ** 3) {
        if (bytes < (1024 ** 3) * 10) {
            return (Math.round((bytes / (1024 ** 3)) * 10) / 10) + 'GB';
        }
        return Math.round(bytes / (1024 ** 3)) + 'GB';
    } else if (bytes > 1024 ** 2) {
        if (bytes < (1024 ** 2) * 10) {
            return (Math.round((bytes / (1024 ** 2)) * 10) / 10) + 'MB';
        }
        return Math.round(bytes / (1024 ** 2)) + 'MB';
    } else if (bytes > 1024) {
        return Math.round(bytes / 1024) + 'KB';
    }
    return bytes + 'B';
}

// Generates a RFC-4122 version 4 compliant globally unique identifier.
export function generateId() {
    // implementation taken from http://stackoverflow.com/a/2117523
    let id = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';

    id = id.replace(/[xy]/g, (c) => {
        const r = Math.floor(Math.random() * 16);

        let v;
        if (c === 'x') {
            v = r;
        } else {
            v = (r & 0x3) | 0x8;
        }

        return v.toString(16);
    });

    return id;
}

export function getDirectChannelName(id: string, otherId: string): string {
    let handle;

    if (otherId > id) {
        handle = id + '__' + otherId;
    } else {
        handle = otherId + '__' + id;
    }

    return handle;
}

// Used to get the id of the other user from a DM channel
export function getUserIdFromChannelName(channel: Channel) {
    return getUserIdFromChannelId(channel.name);
}

// Used to get the id of the other user from a DM channel id (id1_id2)
export function getUserIdFromChannelId(channelId: Channel['id'], currentUserId = getCurrentUserId(store.getState())) {
    const ids = channelId.split('__');
    let otherUserId = '';
    if (ids[0] === currentUserId) {
        otherUserId = ids[1];
    } else {
        otherUserId = ids[0];
    }

    return otherUserId;
}

export function fillRecord<T>(value: T, length: number): Record<number, T> {
    const arr: Record<number, T> = {};

    for (let i = 0; i < length; i++) {
        arr[i] = value;
    }

    return arr;
}

// Checks if a data transfer contains files not text, folders, etc..
// Slightly modified from http://stackoverflow.com/questions/6848043/how-do-i-detect-a-file-is-being-dragged-rather-than-a-draggable-element-on-my-pa
export function isFileTransfer(files: DataTransfer) {
    if (UserAgent.isInternetExplorer() || UserAgent.isEdge()) {
        return files.types != null && files.types.includes('Files');
    }

    return files.types != null && (files.types.indexOf ? files.types.indexOf('Files') !== -1 : files.types.includes('application/x-moz-file'));
}

export function isUriDrop(dataTransfer: DataTransfer) {
    if (UserAgent.isInternetExplorer() || UserAgent.isEdge() || UserAgent.isSafari()) {
        for (let i = 0; i < dataTransfer.items.length; i++) {
            if (dataTransfer.items[i].type === 'text/uri-list') {
                return true;
            }
        }
    }
    return false; // we don't care about others, they handle as we want it
}

export function isTextTransfer(dataTransfer: DataTransfer) {
    return ['text/plain', 'text/unicode', 'Text'].some((type) => dataTransfer.types.includes(type));
}

export function isTextDroppableEvent(e: Event) {
    return (e instanceof DragEvent) &&
           (e.target instanceof HTMLTextAreaElement || e.target instanceof HTMLInputElement) &&
           e.dataTransfer !== null &&
           isTextTransfer(e.dataTransfer);
}

export function clearFileInput(elm: HTMLInputElement) {
    // clear file input for all modern browsers
    try {
        elm.value = '';
        if (elm.value) {
            elm.type = 'text';
            elm.type = 'file';
        }
    } catch (e) {
        // Do nothing
    }
}

/**
 * @deprecated Use react-intl instead, only place its usage can be justified is in the redux actions
 */
export function localizeMessage({id, defaultMessage}: {id: string; defaultMessage?: string}) {
    const state = store.getState();

    const locale = getCurrentLocale(state);
    const translations = getTranslations(state, locale);

    if (!translations || !(id in translations)) {
        return defaultMessage || id;
    }

    return translations[id];
}

/**
 * @deprecated If possible, use intl.formatMessage instead. If you have to use this, remember to mark the id using `t`
 */
export function localizeAndFormatMessage(descriptor: {id: string; defaultMessage?: string}, template: { [name: string]: string | number } | undefined) {
    const base = localizeMessage(descriptor);

    if (!template) {
        return base;
    }

    return base.replace(/{[\w]+}/g, (match) => {
        const key = match.slice(1, -1);
        return String(template[key] || match);
    });
}

export function mod(a: number, b: number): number {
    return ((a % b) + b) % b;
}

export const REACTION_PATTERN = /^(\+|-):([^:\s]+):\s*$/;

function isChannelOrPermalink(link: string) {
    let match = (/\/([a-z0-9\-_]+)\/channels\/([a-z0-9\-__][a-z0-9\-__.]+)/).exec(link);
    if (match) {
        return {
            type: 'channel',
            teamName: match[1],
            channelName: match[2],
        };
    }
    match = (/\/([a-z0-9\-__]+)\/pl\/([a-z0-9]+)/).exec(link);
    if (match) {
        return {
            type: 'permalink',
            teamName: match[1],
            postId: match[2],
        };
    }
    return match;
}

export async function handleFormattedTextClick(e: React.MouseEvent, currentRelativeTeamUrl = '') {
    const target = e.target as Element;
    const hashtagAttribute = target.getAttributeNode('data-hashtag');
    const linkAttribute = target.getAttributeNode('data-link');
    const channelMentionAttribute = target.getAttributeNode('data-channel-mention');

    if (hashtagAttribute) {
        e.preventDefault();

        store.dispatch(searchForTerm(hashtagAttribute.value));
    } else if (linkAttribute) {
        const MIDDLE_MOUSE_BUTTON = 1;

        if (!(e.button === MIDDLE_MOUSE_BUTTON || e.altKey || e.ctrlKey || e.metaKey || e.shiftKey)) {
            e.preventDefault();

            const state = store.getState();
            const user = getCurrentUser(state);
            const match = isChannelOrPermalink(linkAttribute.value);
            const crtEnabled = isCollapsedThreadsEnabled(state);

            let isReply = false;

            if (isSystemAdmin(user.roles)) {
                if (match) {
                    // Get team by name
                    const {teamName} = match;
                    let team = getTeamByName(state, teamName);
                    if (!team) {
                        const {data: teamData} = await store.dispatch(getTeamByNameAction(teamName));
                        team = teamData;
                    }
                    if (team && team.delete_at === 0) {
                        let channel;

                        // Handle channel url - Get channel data from channel name
                        if (match.type === 'channel') {
                            const {channelName} = match;
                            channel = getChannelsNameMapInTeam(state, team.id)[channelName as string];
                            if (!channel) {
                                const {data: channelData} = await store.dispatch(getChannelByNameAndTeamName(teamName, channelName!, true));
                                channel = channelData;
                            }
                        } else { // Handle permalink - Get channel data from post
                            const {postId} = match;
                            let post = getPost(state, postId!);
                            if (!post) {
                                const {data: postData} = await store.dispatch(getPostAction(match.postId!));
                                post = postData;
                            }
                            if (post) {
                                isReply = Boolean(post.root_id);

                                channel = getChannel(state, post.channel_id);
                                if (!channel) {
                                    const {data: channelData} = await store.dispatch(getChannelAction(post.channel_id));
                                    channel = channelData;
                                }
                            }
                        }
                        if (channel && channel.type === Constants.PRIVATE_CHANNEL) {
                            let member = getMyChannelMemberships(state)[channel.id];
                            if (!member) {
                                const membership = await store.dispatch(getChannelMember(channel.id, getCurrentUserId(state)));
                                if ('data' in membership) {
                                    member = membership.data;
                                }
                            }
                            if (!member) {
                                const {data} = await store.dispatch(joinPrivateChannelPrompt(team, channel.display_name, false));
                                if (data.join) {
                                    let error = false;
                                    if (!getTeamMemberships(state)[team.id]) {
                                        const joinTeamResult = await store.dispatch(addUserToTeam(team.id, user.id));
                                        error = joinTeamResult.error;
                                    }
                                    if (!error) {
                                        await store.dispatch(joinChannel(user.id, team.id, channel.id, channel.name));
                                    }
                                } else {
                                    return;
                                }
                            }
                        }
                    }
                }
            }

            e.stopPropagation();

            if (match && match.type === 'permalink' && isTeamSameWithCurrentTeam(state, match.teamName) && isReply && crtEnabled) {
                store.dispatch(focusPost(match.postId ?? '', linkAttribute.value, user.id, {skipRedirectReplyPermalink: true}));
            } else {
                getHistory().push(linkAttribute.value);
            }
        }
    } else if (channelMentionAttribute) {
        e.preventDefault();
        getHistory().push(currentRelativeTeamUrl + '/channels/' + channelMentionAttribute.value);
    }
}

export function isEmptyObject(object: Record<string, unknown> | null | undefined): boolean {
    if (!object) {
        return true;
    }

    if (Object.keys(object).length === 0) {
        return true;
    }

    return false;
}

export function removePrefixFromLocalStorage(prefix: string) {
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
        if (localStorage.key(i)!.startsWith(prefix)) {
            keys.push(localStorage.key(i));
        }
    }

    for (let i = 0; i < keys.length; i++) {
        localStorage.removeItem(keys[i]!);
    }
}

export function copyToClipboard(data: string) {
    // Attempt to use the newer clipboard API when possible
    const clipboard = navigator.clipboard;
    if (clipboard) {
        clipboard.writeText(data);
        return;
    }

    // creates a tiny temporary text area to copy text out of
    // see https://stackoverflow.com/a/30810322/591374 for details
    const textArea = document.createElement('textarea');
    textArea.style.position = 'fixed';
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.width = '2em';
    textArea.style.height = '2em';
    textArea.style.padding = '0';
    textArea.style.border = 'none';
    textArea.style.outline = 'none';
    textArea.style.boxShadow = 'none';
    textArea.style.background = 'transparent';
    textArea.value = data;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
    } catch (err) {
        console.warn('Failed to copy text to clipboard:', err); // eslint-disable-line no-console
    } finally {
        document.body.removeChild(textArea);
    }
}

export function moveCursorToEnd(e: React.MouseEvent | React.FocusEvent) {
    const target = e.target as HTMLInputElement | HTMLTextAreaElement;
    const val = target.value;
    if (val.length) {
        target.value = '';
        target.value = val;
    }
}

export function setCSRFFromCookie() {
    if (typeof document !== 'undefined' && typeof document.cookie !== 'undefined') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('MMCSRF=')) {
                Client4.setCSRF(cookie.replace('MMCSRF=', ''));
                break;
            }
        }
    }
}

export function getNextBillingDate() {
    const nextBillingDate = moment().add(1, 'months').startOf('month');
    return nextBillingDate.format('MMM D, YYYY');
}

export function stringToNumber(s: string | undefined) {
    if (!s) {
        return 0;
    }

    return parseInt(s, 10);
}

export function deleteKeysFromObject(value: Record<string, unknown>, keys: string[]): Record<string, unknown> {
    for (const key of keys) {
        delete value[key];
    }
    return value;
}

function isSelection() {
    const selection = window.getSelection();
    return selection!.type === 'Range';
}

export function isTextSelectedInPostOrReply(e: React.KeyboardEvent | KeyboardEvent) {
    const {id} = e.target as HTMLElement;

    const isTypingInPost = id === 'post_textbox';
    const isTypingInReply = id === 'reply_textbox';

    if (!isTypingInPost && !isTypingInReply) {
        return false;
    }

    const {
        selectionStart,
        selectionEnd,
    } = e.target as TextboxElement;

    const hasSelection = !isNil(selectionStart) && !isNil(selectionEnd) && selectionStart < selectionEnd;

    return hasSelection;
}

/*
 * Returns false when the element clicked or its ancestors
 * is a potential click target (link, button, image, etc..)
 * but not the events currentTarget
 * and true in any other case.
 *
 * @param {string} selector - CSS selector of elements not eligible for click
 * @returns {function}
 */
export function makeIsEligibleForClick(selector = '') {
    return (event: React.MouseEvent) => {
        const currentTarget = event.currentTarget;
        let node: HTMLElement = event.target as HTMLElement;

        if (isSelection()) {
            return false;
        }

        if (node === currentTarget) {
            return true;
        }

        // in the case of a react Portal
        if (!currentTarget.contains(node)) {
            return false;
        }

        // traverses the targets parents up to currentTarget to see
        // if any of them is a potentially clickable element
        while (node) {
            if (!node || node === currentTarget) {
                break;
            }

            if (
                CLICKABLE_ELEMENTS.includes(node.tagName.toLowerCase()) ||
                node.getAttribute('role') === 'button' ||
                (selector && node.matches(selector))
            ) {
                return false;
            }

            node = node.parentNode! as HTMLElement;
        }

        return true;
    };
}

// Returns the minimal number of zeroes needed to render a number,
// up to the given number of places.
// e.g.
// numberToFixedDynamic(3.12345, 4) -> 3.1235
// numberToFixedDynamic(3.01000, 4) -> 3.01
// numberToFixedDynamic(3.01000, 1) -> 3
export function numberToFixedDynamic(num: number, places: number): string {
    const str = num.toFixed(Math.max(places, 0));
    if (!str.includes('.')) {
        return str;
    }
    let indexToExclude = -1;
    let i = str.length - 1;
    while (str[i] === '0') {
        indexToExclude = i;
        i -= 1;
    }
    if (str[i] === '.') {
        indexToExclude -= 1;
    }
    if (indexToExclude === -1) {
        return str;
    }
    return str.slice(0, indexToExclude);
}

const TrackFlowRoles: Record<string, string> = {
    fa: Constants.FIRST_ADMIN_ROLE,
    sa: General.SYSTEM_ADMIN_ROLE,
    su: General.SYSTEM_USER_ROLE,
};

export function getTrackFlowRole(state: GlobalState) {
    let trackFlowRole = 'su';

    if (isFirstAdmin(state)) {
        trackFlowRole = 'fa';
    } else if (isSystemAdmin(getCurrentUser(state).roles)) {
        trackFlowRole = 'sa';
    }

    return trackFlowRole;
}

export const getRoleForTrackFlow = createSelector(
    'getRoleForTrackFlow',
    getTrackFlowRole,
    (trackFlowRole) => {
        const startedByRole = TrackFlowRoles[trackFlowRole];

        return {started_by_role: startedByRole};
    },
);

export function getSbr() {
    const params = new URLSearchParams(window.location.search);
    const sbr = params.get('sbr') ?? '';
    return sbr;
}

export function getRoleFromTrackFlow() {
    const sbr = getSbr();
    const startedByRole = TrackFlowRoles[sbr] ?? '';

    return {started_by_role: startedByRole};
}

export function getDatePickerLocalesForDateFns(locale: string, loadedLocales: Record<string, Locale>) {
    if (locale === 'ar' && !loadedLocales.ar) {
        loadedLocales.ar = ar; // ✅ إضافة دعم العربية
    } else if (locale && !loadedLocales[locale]) {
        try {
            // Dynamic import for date-fns locales
            // eslint-disable-next-line @typescript-eslint/no-var-requires, import/no-dynamic-require
            loadedLocales[locale] = require(`date-fns/locale/${locale}/index.js`);
        } catch (e) {
            console.log(e); // eslint-disable-line no-console
        }
    }

    return loadedLocales;
}
export function getMediumFromTrackFlow() {
    const params = new URLSearchParams(window.location.search);
    const source = params.get('md') ?? '';

    return {source};
}

const TrackFlowSources: Record<string, string> = {
    wd: 'webapp-desktop',
    wm: 'webapp-mobile',
    d: 'desktop-app',
};

function getTrackFlowSource() {
    if (UserAgent.isMobile()) {
        return TrackFlowSources.wm;
    } else if (UserAgent.isDesktopApp()) {
        return TrackFlowSources.d;
    }
    return TrackFlowSources.wd;
}

export function getSourceForTrackFlow() {
    return {source: getTrackFlowSource()};
}

export function a11yFocus(element: HTMLElement | null | undefined, keyboardOnly = true) {
    document.dispatchEvent(new CustomEvent<A11yFocusEventDetail>(
        A11yCustomEventTypes.FOCUS, {
            detail: {
                target: element,
                keyboardOnly,
            },
        },
    ));
}

export function getBlankAddressWithCountry(country?: string): Address {
    let c = '';
    if (country) {
        c = getName(country) || '';
    }
    return {
        city: '',
        country: c || '',
        line1: '',
        line2: '',
        postal_code: '',
        state: '',
    };
}

export function generateSlug(): string {
    return crypto.randomBytes(16).toString('hex');
}
export function sortUsersAndGroups(a: UserProfile | Group, b: UserProfile | Group) {
    let aSortString = '';
    let bSortString = '';
    if ('username' in a) {
        aSortString = a.username;
    } else {
        aSortString = a.name;
    }
    if ('username' in b) {
        bSortString = b.username;
    } else {
        bSortString = b.name;
    }

    return aSortString.localeCompare(bSortString);
}

export function doesCookieContainsMMUserId() {
    return document.cookie.includes('MMUSERID=');
}
