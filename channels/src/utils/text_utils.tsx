// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {getCurrentLocale, getTranslations} from 'selectors/i18n';
import store from 'stores/redux_store';

export function createSafeId(prop: {props: {defaultMessage: string}} | string): string | undefined {
    let str = '';

    if (typeof prop !== 'string' && prop.props && prop.props.defaultMessage) {
        str = prop.props.defaultMessage;
    } else {
        str = prop.toString();
    }

    return str.replace(new RegExp(' ', 'g'), '_');
}

export function replaceHtmlEntities(text: string) {
    const tagsToReplace = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
    };
    let newtext = text;
    Object.entries(tagsToReplace).forEach(([tag, replacement]) => {
        const regex = new RegExp(tag, 'g');
        newtext = newtext.replace(regex, replacement);
    });
    return newtext;
}

export function toTitleCase(str: string): string {
    function doTitleCase(txt: string) {
        return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();
    }
    return str.replace(/\w\S*/g, doTitleCase);
}

export function localizeMessage(descriptor: {id: string; defaultMessage?: string}): string {
    const {id, defaultMessage = ''} = descriptor;
    const state = store.getState();
    const locale = getCurrentLocale(state);
    const translations = getTranslations(state, locale);

    if (!translations || !translations[id]) {
        return defaultMessage || id;
    }

    return translations[id];
}

/**
 * @deprecated If possible, use intl.formatMessage instead. If you have to use this, remember to mark the id using `t`
 */
export function localizeAndFormatMessage(descriptor: {id: string; defaultMessage?: string}, template: { [name: string]: string | number } | undefined) {
    const base = localizeMessage(descriptor);

    if (!template) {
        return base;
    }

    return base.replace(/{[\w]+}/g, (match) => {
        const key = match.slice(1, -1);
        return template[key] || match;
    });
}

export function mod(a: number, b: number): number {
    return ((a % b) + b) % b;
}

export function isEmptyObject(object: Record<string, unknown> | null | undefined): boolean {
    if (!object) {
        return true;
    }

    if (Object.keys(object).length === 0) {
        return true;
    }

    return false;
}

export function deleteKeysFromObject(value: Record<string, unknown>, keys: string[]): Record<string, unknown> {
    for (const key of keys) {
        delete value[key];
    }
    return value;
}

export function tryParseInt(s: string, defaultValue = 0): number {
    if (!s) {
        return defaultValue;
    }

    const parsed = parseInt(s, 10);
    if (isNaN(parsed)) {
        return 0;
    }

    return parseInt(s, 10);
}
